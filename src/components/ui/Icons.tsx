import { 
  Smartphone, 
  Monitor, 
  Server, 
  Globe, 
  Zap, 
  Users, 
  Award, 
  CheckCircle, 
  ArrowRight, 
  Mail, 
  Phone, 
  MapPin, 
  ExternalLink,
  Github,
  Linkedin,
  Twitter,
  Star,
  Quote,
  Code,
  Palette,
  Database,
  Cloud,
  Shield,
  Rocket,
  Target,
  TrendingUp,
  Heart,
  Eye,
  MessageCircle,
  Send,
  Download,
  Upload,
  Search,
  Filter,
  Settings,
  Menu,
  X,
  ChevronDown,
  ChevronUp,
  ChevronLeft,
  ChevronRight,
  Play,
  Pause,
  Volume2,
  VolumeX,
  Maximize,
  Minimize,
  RotateCcw,
  RefreshCw,
  Home,
  User,
  Building,
  Briefcase,
  GraduationCap,
  Calendar,
  Clock,
  MapPinIcon,
  type LucideIcon
} from 'lucide-react';

// 服务相关图标
export const ServiceIcons = {
  AppDev: Smartphone,
  MiniProgram: Monitor,
  Backend: Server,
  WebDev: Globe,
  GlobalPlatforms: Globe,
  Performance: Zap,
  Team: Users,
  Quality: Award,
  Success: CheckCircle,
} as const;

// 导航和交互图标
export const NavigationIcons = {
  Arrow: ArrowRight,
  Menu: Menu,
  Close: X,
  ChevronDown: ChevronDown,
  ChevronUp: ChevronUp,
  ChevronLeft: ChevronLeft,
  ChevronRight: ChevronRight,
  Home: Home,
  User: User,
  Building: Building,
  Briefcase: Briefcase,
  CheckCircle: CheckCircle,
} as const;

// 联系方式图标
export const ContactIcons = {
  Email: Mail,
  Phone: Phone,
  Location: MapPin,
  ExternalLink: ExternalLink,
  Send: Send,
} as const;

// 社交媒体图标
export const SocialIcons = {
  Github: Github,
  Linkedin: Linkedin,
  Twitter: Twitter,
} as const;

// 功能图标
export const FeatureIcons = {
  Star: Star,
  Quote: Quote,
  Code: Code,
  Design: Palette,
  Database: Database,
  Cloud: Cloud,
  Security: Shield,
  Launch: Rocket,
  Target: Target,
  Growth: TrendingUp,
  Like: Heart,
  View: Eye,
  Comment: MessageCircle,
} as const;

// 媒体控制图标
export const MediaIcons = {
  Play: Play,
  Pause: Pause,
  VolumeOn: Volume2,
  VolumeOff: VolumeX,
  Fullscreen: Maximize,
  ExitFullscreen: Minimize,
} as const;

// 工具图标
export const ToolIcons = {
  Search: Search,
  Filter: Filter,
  Settings: Settings,
  Download: Download,
  Upload: Upload,
  Refresh: RefreshCw,
  Reset: RotateCcw,
  Calendar: Calendar,
  Clock: Clock,
  Education: GraduationCap,
} as const;

// 图标组件接口
interface IconProps {
  className?: string;
  size?: number;
  color?: string;
}

// 通用图标组件
export const Icon = ({ 
  icon: IconComponent, 
  className = '', 
  size = 24, 
  color = 'currentColor',
  ...props 
}: IconProps & { icon: LucideIcon }) => (
  <IconComponent 
    className={className} 
    size={size} 
    color={color} 
    {...props} 
  />
);

// 预设图标组件
export const AppDevIcon = (props: IconProps) => <Icon icon={ServiceIcons.AppDev} {...props} />;
export const MiniProgramIcon = (props: IconProps) => <Icon icon={ServiceIcons.MiniProgram} {...props} />;
export const BackendIcon = (props: IconProps) => <Icon icon={ServiceIcons.Backend} {...props} />;
export const WebDevIcon = (props: IconProps) => <Icon icon={ServiceIcons.WebDev} {...props} />;
export const GlobalPlatformsIcon = (props: IconProps) => <Icon icon={ServiceIcons.GlobalPlatforms} {...props} />;

export const EmailIcon = (props: IconProps) => <Icon icon={ContactIcons.Email} {...props} />;
export const PhoneIcon = (props: IconProps) => <Icon icon={ContactIcons.Phone} {...props} />;
export const LocationIcon = (props: IconProps) => <Icon icon={ContactIcons.Location} {...props} />;

export const StarIcon = (props: IconProps) => <Icon icon={FeatureIcons.Star} {...props} />;
export const QuoteIcon = (props: IconProps) => <Icon icon={FeatureIcons.Quote} {...props} />;
export const CodeIcon = (props: IconProps) => <Icon icon={FeatureIcons.Code} {...props} />;

// 导出所有图标类型
export type ServiceIconType = keyof typeof ServiceIcons;
export type NavigationIconType = keyof typeof NavigationIcons;
export type ContactIconType = keyof typeof ContactIcons;
export type SocialIconType = keyof typeof SocialIcons;
export type FeatureIconType = keyof typeof FeatureIcons;
export type MediaIconType = keyof typeof MediaIcons;
export type ToolIconType = keyof typeof ToolIcons;
