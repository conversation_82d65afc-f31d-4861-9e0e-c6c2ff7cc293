'use client';

import Link from 'next/link';
import { t, type Locale } from '@/lib/i18n';

interface FooterProps {
  locale: Locale;
}

const Footer = ({ locale }: FooterProps) => {
  const currentYear = new Date().getFullYear();
  
  return (
    <footer className="bg-gray-100 mt-auto">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="md:flex md:justify-between">
          <div className="mb-8 md:mb-0">
            <Link href={`/${locale}`} className="text-xl font-bold text-primary">
              <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent hover:from-blue-500 hover:via-purple-500 hover:to-indigo-500 transition-all duration-300">
                {locale === 'zh' ? '三娃软件开发工作室' : 'Sanva Studio'}
              </span>
            </Link>
            <p className="mt-2 text-sm text-gray-500">
              {locale === 'zh' ? '打造高效、优雅的数字解决方案' : 'Crafting Efficient & Elegant Digital Solutions'}
            </p>
          </div>
          <div className="grid grid-cols-2 gap-8 sm:grid-cols-3">
            <div>
              <h3 className="text-sm font-medium text-gray-900">
                {locale === 'zh' ? '导航' : 'Navigation'}
              </h3>
              <ul className="mt-4 space-y-2">
                <li>
                  <Link href={`/${locale}`} className="text-sm text-gray-500 hover:text-gray-900">
                    {t('navigation.home', locale)}
                  </Link>
                </li>
                <li>
                  <Link href={`/${locale}/about`} className="text-sm text-gray-500 hover:text-gray-900">
                    {t('navigation.about', locale)}
                  </Link>
                </li>
                <li>
                  <Link href={`/${locale}/services`} className="text-sm text-gray-500 hover:text-gray-900">
                    {t('navigation.services', locale)}
                  </Link>
                </li>
                <li>
                  <Link href={`/${locale}/contact`} className="text-sm text-gray-500 hover:text-gray-900">
                    {t('navigation.contact', locale)}
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-900">
                {locale === 'zh' ? '服务' : 'Services'}
              </h3>
              <ul className="mt-4 space-y-2">
                <li>
                  <Link href={`/${locale}/services`} className="text-sm text-gray-500 hover:text-gray-900">
                    {locale === 'zh' ? '应用开发' : 'App Development'}
                  </Link>
                </li>
                <li>
                  <Link href={`/${locale}/services`} className="text-sm text-gray-500 hover:text-gray-900">
                    {locale === 'zh' ? '小程序开发' : 'Mini-Program Development'}
                  </Link>
                </li>
                <li>
                  <Link href={`/${locale}/services`} className="text-sm text-gray-500 hover:text-gray-900">
                    {locale === 'zh' ? '后端开发' : 'Backend Development'}
                  </Link>
                </li>
                <li>
                  <Link href={`/${locale}/services`} className="text-sm text-gray-500 hover:text-gray-900">
                    {locale === 'zh' ? '海外平台开发' : 'Global Platform Development'}
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-900">
                {locale === 'zh' ? '联系方式' : 'Contact'}
              </h3>
              <ul className="mt-4 space-y-2">
                <li>
                  <a href="mailto:<EMAIL>" className="text-sm text-gray-500 hover:text-gray-900">
                    <EMAIL>
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div className="mt-8 border-t border-gray-200 pt-8 md:flex md:items-center md:justify-between">
          <p className="mt-8 text-base text-gray-400 md:mt-0 md:order-1">
            &copy; {currentYear} {locale === 'zh' ? '三娃软件开发工作室' : 'Sanva Software Development Studio'}. {locale === 'zh' ? '版权所有' : 'All rights reserved'}. 
            {locale === 'zh' && (
              <>
                {' '}
                <a href="https://beian.miit.gov.cn/" target="_blank" rel="noopener noreferrer" className="hover:text-gray-600">
                  闽ICP备2024074836号-2
                </a>
              </>
            )}
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer; 