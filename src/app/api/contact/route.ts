import { NextRequest, NextResponse } from 'next/server';
import nodemailer from 'nodemailer';

const smtpConfig = {
  host: 'mail.sanva.tk',
  port: 465,
  secure: true, // true for 465, false for other ports
  auth: {
    user: '<EMAIL>',
    pass: '1GNgnesAn53Kf+Of'
  }
};

// 多语言确认邮件模板
const getConfirmationEmailTemplate = (language: string, name: string) => {
  const templates = {
    'zh': {
      subject: '感谢您的咨询 - Sanva',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; border-radius: 10px 10px 0 0; text-align: center;">
            <h1 style="color: white; margin: 0; font-size: 24px;">感谢您的咨询</h1>
            <p style="color: #e0e6ff; margin: 10px 0 0 0; font-size: 16px;">Sanva 团队</p>
          </div>
          
          <div style="background: white; padding: 30px; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
            <p style="color: #333; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;">亲爱的 ${name}，</p>
            
            <p style="color: #555; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;">
              感谢您通过我们的官网联系我们！我们已经收到了您的咨询信息，我们的团队将在 <strong>24小时内</strong> 与您取得联系。
            </p>
            
            <div style="background: #f8f9ff; padding: 20px; border-radius: 8px; border-left: 4px solid #667eea; margin: 20px 0;">
              <p style="color: #555; font-size: 16px; line-height: 1.6; margin: 0;">
                💼 我们专注于为客户提供优质的服务体验<br/>
                🚀 专业团队将为您量身定制解决方案<br/>
                📞 如有紧急需求，请直接联系我们：<EMAIL>
              </p>
            </div>
            
            <p style="color: #555; font-size: 16px; line-height: 1.6; margin: 20px 0 0 0;">
              再次感谢您对 Sanva 的关注与信任！
            </p>
            
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
              <p style="color: #333; font-size: 16px; margin: 0;"><strong>Sanva 团队</strong></p>
              <p style="color: #888; font-size: 14px; margin: 5px 0 0 0;">www.sanva.tk</p>
            </div>
          </div>
        </div>
      `,
      text: `
亲爱的 ${name}，

感谢您通过我们的官网联系我们！我们已经收到了您的咨询信息，我们的团队将在24小时内与您取得联系。

我们专注于为客户提供优质的服务体验，专业团队将为您量身定制解决方案。

如有紧急需求，请直接联系我们：<EMAIL>

再次感谢您对 Sanva 的关注与信任！

Sanva 团队
www.sanva.tk
      `.trim()
    },
    'en': {
      subject: 'Thank you for your inquiry - Sanva',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; border-radius: 10px 10px 0 0; text-align: center;">
            <h1 style="color: white; margin: 0; font-size: 24px;">Thank You for Your Inquiry</h1>
            <p style="color: #e0e6ff; margin: 10px 0 0 0; font-size: 16px;">Sanva Team</p>
          </div>
          
          <div style="background: white; padding: 30px; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
            <p style="color: #333; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;">Dear ${name},</p>
            
            <p style="color: #555; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;">
              Thank you for contacting us through our website! We have received your inquiry and our team will get back to you within <strong>24 hours</strong>.
            </p>
            
            <div style="background: #f8f9ff; padding: 20px; border-radius: 8px; border-left: 4px solid #667eea; margin: 20px 0;">
              <p style="color: #555; font-size: 16px; line-height: 1.6; margin: 0;">
                💼 We focus on providing excellent service experience<br/>
                🚀 Our professional team will create customized solutions for you<br/>
                📞 For urgent matters, please contact us directly: <EMAIL>
              </p>
            </div>
            
            <p style="color: #555; font-size: 16px; line-height: 1.6; margin: 20px 0 0 0;">
              Thank you again for your interest and trust in Sanva!
            </p>
            
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
              <p style="color: #333; font-size: 16px; margin: 0;"><strong>Sanva Team</strong></p>
              <p style="color: #888; font-size: 14px; margin: 5px 0 0 0;">www.sanva.tk</p>
            </div>
          </div>
        </div>
      `,
      text: `
Dear ${name},

Thank you for contacting us through our website! We have received your inquiry and our team will get back to you within 24 hours.

We focus on providing excellent service experience, and our professional team will create customized solutions for you.

For urgent matters, please contact us directly: <EMAIL>

Thank you again for your interest and trust in Sanva!

Sanva Team
www.sanva.tk
      `.trim()
    }
  };

  return templates[language as keyof typeof templates] || templates['en'];
};

// 检测语言的函数
const detectLanguage = (text: string): string => {
  // 检测中文字符
  const chineseRegex = /[\u4e00-\u9fff]/;
  if (chineseRegex.test(text)) {
    return 'zh';
  }
  return 'en';
};

export async function POST(request: NextRequest) {
  try {
    const { name, email, phone, message, language } = await request.json();

    // 验证必填字段
    if (!name || !email || !message) {
      return NextResponse.json(
        { error: '请填写所有必填字段' },
        { status: 400 }
      );
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: '请输入有效的邮箱地址' },
        { status: 400 }
      );
    }

    // 创建邮件传输器
    const transporter = nodemailer.createTransport(smtpConfig);

    // 检测客户使用的语言
    const customerLanguage = language || detectLanguage(message);

    // 发送给管理员的邮件选项（保持中文）
    const adminMailOptions = {
      from: '"Sanva 联系表单" <<EMAIL>>',
      to: '<EMAIL>',
      subject: `来自网站的新联系消息 - ${name}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; border-radius: 10px 10px 0 0; text-align: center;">
            <h1 style="color: white; margin: 0; font-size: 24px;">新的联系消息</h1>
            <p style="color: #e0e6ff; margin: 10px 0 0 0; font-size: 16px;">来自 Sanva 官方网站</p>
          </div>
          
          <div style="background: white; padding: 30px; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
            <div style="margin-bottom: 25px;">
              <h3 style="color: #333; margin: 0 0 10px 0; font-size: 18px; border-bottom: 2px solid #667eea; padding-bottom: 5px;">联系人信息</h3>
              <p style="margin: 8px 0; color: #555; font-size: 16px;"><strong>姓名：</strong> ${name}</p>
              <p style="margin: 8px 0; color: #555; font-size: 16px;"><strong>邮箱：</strong> <a href="mailto:${email}" style="color: #667eea; text-decoration: none;">${email}</a></p>
              ${phone ? `<p style="margin: 8px 0; color: #555; font-size: 16px;"><strong>电话：</strong> ${phone}</p>` : ''}
              <p style="margin: 8px 0; color: #555; font-size: 16px;"><strong>客户语言：</strong> ${customerLanguage === 'zh' ? '中文' : '英文'}</p>
            </div>
            
            <div style="margin-bottom: 25px;">
              <h3 style="color: #333; margin: 0 0 15px 0; font-size: 18px; border-bottom: 2px solid #667eea; padding-bottom: 5px;">需求描述</h3>
              <div style="background: #f8f9ff; padding: 20px; border-radius: 8px; border-left: 4px solid #667eea;">
                <p style="margin: 0; color: #555; line-height: 1.6; font-size: 16px; white-space: pre-wrap;">${message}</p>
              </div>
            </div>
            
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; text-align: center;">
              <p style="color: #888; margin: 0; font-size: 14px;">
                <strong>提交时间：</strong> ${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}
              </p>
              <p style="color: #888; margin: 10px 0 0 0; font-size: 14px;">
                请及时回复客户咨询，提升服务质量。已自动发送确认邮件给客户。
              </p>
            </div>
          </div>
          
          <div style="text-align: center; margin-top: 20px; color: #888; font-size: 12px;">
            <p style="margin: 0;">此邮件由 Sanva 官网联系表单自动发送</p>
          </div>
        </div>
      `,
      // 纯文本版本作为备用
      text: `
新的联系消息

联系人信息：
姓名：${name}
邮箱：${email}
${phone ? `电话：${phone}` : ''}
客户语言：${customerLanguage === 'zh' ? '中文' : '英文'}

需求描述：
${message}

提交时间：${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}
      `.trim()
    };

    // 获取确认邮件模板
    const confirmationTemplate = getConfirmationEmailTemplate(customerLanguage, name);

    // 发送给客户的确认邮件选项
    const customerMailOptions = {
      from: '"Sanva Team" <<EMAIL>>',
      to: email,
      subject: confirmationTemplate.subject,
      html: confirmationTemplate.html,
      text: confirmationTemplate.text
    };

    // 同时发送两封邮件
    await Promise.all([
      transporter.sendMail(adminMailOptions),
      transporter.sendMail(customerMailOptions)
    ]);

    return NextResponse.json(
      { message: '邮件发送成功' },
      { status: 200 }
    );

  } catch (error) {
    console.error('Error sending email:', error);
    return NextResponse.json(
      { error: '邮件发送失败，请稍后重试' },
      { status: 500 }
    );
  }
} 