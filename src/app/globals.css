@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@300;400;500;600;700&display=swap');

:root {
  --background: #ffffff;
  --foreground: #0f172a;
  --muted: #f8fafc;
  --muted-foreground: #64748b;
  --border: #e2e8f0;
  --input: #ffffff;
  --card: #ffffff;
  --card-foreground: #0f172a;
  --primary: #3b82f6;
  --primary-foreground: #ffffff;
  --secondary: #10b981;
  --secondary-foreground: #ffffff;
  --accent: #d946ef;
  --accent-foreground: #ffffff;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --ring: #3b82f6;
  --radius: 0.5rem;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: 'Inter', system-ui, sans-serif;
  --font-display: 'Poppins', system-ui, sans-serif;
  --font-mono: 'JetBrains Mono', monospace;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #020617;
    --foreground: #f8fafc;
    --muted: #0f172a;
    --muted-foreground: #94a3b8;
    --border: #1e293b;
    --input: #1e293b;
    --card: #0f172a;
    --card-foreground: #f8fafc;
    --primary: #60a5fa;
    --primary-foreground: #1e293b;
    --secondary: #34d399;
    --secondary-foreground: #1e293b;
    --accent: #f0abfc;
    --accent-foreground: #1e293b;
    --destructive: #f87171;
    --destructive-foreground: #1e293b;
    --ring: #60a5fa;
  }
}

* {
  border-color: var(--border);
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--muted);
}

::-webkit-scrollbar-thumb {
  background: var(--muted-foreground);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary);
}

/* 选择文本样式 */
::selection {
  background: var(--primary);
  color: var(--primary-foreground);
}

/* 焦点样式 */
:focus-visible {
  outline: 2px solid var(--ring);
  outline-offset: 2px;
}

/* 渐变文本工具类 */
.gradient-text {
  background: linear-gradient(135deg, var(--primary), var(--accent));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 玻璃态效果 */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 卡片悬停效果 */
.card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px -12px rgba(0, 0, 0, 0.15);
}

/* 按钮动画 */
.btn-animate {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.btn-animate::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-animate:hover::before {
  left: 100%;
}

/* 加载动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 响应式字体大小 */
.text-responsive {
  font-size: clamp(1rem, 2.5vw, 1.5rem);
}

.text-responsive-lg {
  font-size: clamp(1.5rem, 4vw, 3rem);
}

.text-responsive-xl {
  font-size: clamp(2rem, 5vw, 4rem);
}
