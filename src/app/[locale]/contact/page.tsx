'use client';

import { motion } from "framer-motion";
import { useState } from "react";
import Head from "next/head";
import { useParams } from "next/navigation";
import { t, type Locale } from '@/lib/i18n';

// 动画配置
const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

// 联系方式组件
const ContactMethod = ({ icon, title, value, link }: { icon: string; title: string; value: string; link?: string }) => (
  <motion.div
    className="flex items-start p-4 bg-white rounded-lg shadow-md"
    initial="hidden"
    whileInView="visible"
    viewport={{ once: true }}
    variants={fadeInUp}
    transition={{ duration: 0.5 }}
  >
    <div className="text-3xl text-primary mr-4">{icon}</div>
    <div>
      <h3 className="text-md font-medium text-gray-900">{title}</h3>
      {link ? (
        <a href={link} className="text-primary hover:text-blue-700 hover:underline">
          {value}
        </a>
      ) : (
        <p className="text-gray-600">{value}</p>
      )}
    </div>
  </motion.div>
);

export default function Contact() {
  const params = useParams();
  const locale = params.locale as string;
  const localeTyped = locale as Locale;

  // 表单状态
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    message: "",
  });
  const [formErrors, setFormErrors] = useState({
    name: "",
    email: "",
    message: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });

    // 清除对应的错误信息
    if (formErrors[name as keyof typeof formErrors]) {
      setFormErrors({
        ...formErrors,
        [name]: "",
      });
    }
  };

  // 表单验证
  const validateForm = () => {
    let valid = true;
    const newErrors = { ...formErrors };

    if (!formData.name.trim()) {
      newErrors.name = t('contact.form.errors.nameRequired', localeTyped);
      valid = false;
    }

    if (!formData.email.trim()) {
      newErrors.email = t('contact.form.errors.emailRequired', localeTyped);
      valid = false;
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email.trim())) {
      newErrors.email = t('contact.form.errors.emailInvalid', localeTyped);
      valid = false;
    }

    if (!formData.message.trim()) {
      newErrors.message = t('contact.form.errors.messageRequired', localeTyped);
      valid = false;
    }

    setFormErrors(newErrors);
    return valid;
  };

  // 提交表单
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      setIsSubmitting(true);

      try {
        const response = await fetch('/api/contact', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            ...formData,
            language: localeTyped
          }),
        });

        if (response.ok) {
          setSubmitted(true);
          // 重置表单
          setFormData({
            name: "",
            email: "",
            phone: "",
            message: "",
          });

          // 5秒后隐藏成功消息
          setTimeout(() => {
            setSubmitted(false);
          }, 5000);
        } else {
          alert(localeTyped === 'zh' ? '发送失败，请稍后重试或直接联系我们的邮箱' : 'Sending failed, please try again later or contact us directly via email');
        }
      } catch (error) {
        console.error('Error submitting form:', error);
        alert(localeTyped === 'zh' ? '网络错误，请稍后重试或直接联系我们的邮箱' : 'Network error, please try again later or contact us directly via email');
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  // 常见问题数据
  const faqs = [
    {
      question: t('contact.faq.questions.timeline.q', localeTyped),
      answer: t('contact.faq.questions.timeline.a', localeTyped)
    },
    {
      question: t('contact.faq.questions.pricing.q', localeTyped),
      answer: t('contact.faq.questions.pricing.a', localeTyped)
    },
    {
      question: t('contact.faq.questions.maintenance.q', localeTyped),
      answer: t('contact.faq.questions.maintenance.a', localeTyped)
    },
    {
      question: t('contact.faq.questions.modification.q', localeTyped),
      answer: t('contact.faq.questions.modification.a', localeTyped)
    }
  ];

  // 结构化数据
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "ContactPage",
    "name": localeTyped === 'zh' ? "联系我们 - 三娃软件开发工作室" : "Contact Us - Sanva Software Development Studio",
    "description": localeTyped === 'zh' ? "联系三娃软件开发工作室，获取专业的软件开发服务咨询" : "Contact Sanva Software Development Studio for professional software development services consultation",
    "url": `https://sanva.top/${locale}/contact`,
    "mainEntity": {
      "@type": "Organization",
      "name": localeTyped === 'zh' ? "三娃软件开发工作室" : "Sanva Software Development Studio",
      "email": "<EMAIL>",
      "url": "https://sanva.top",
      "contactPoint": {
        "@type": "ContactPoint",
        "email": "<EMAIL>",
        "contactType": "customer service",
        "areaServed": "CN",
        "availableLanguage": localeTyped === 'zh' ? "Chinese" : "English"
      }
    }
  };

  return (
    <>
      <Head>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
        />
      </Head>
      {/* 页面标题 */}
      <section className="bg-gradient-to-r from-primary to-blue-700 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.h1 
            className="text-3xl md:text-4xl font-bold mb-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            {t('contact.title', localeTyped)}
          </motion.h1>
          <motion.p 
            className="text-xl text-blue-100 max-w-3xl"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            {t('contact.subtitle', localeTyped)}
          </motion.p>
        </div>
      </section>

      {/* 联系信息和表单 */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="md:flex md:space-x-8">
            {/* 联系信息 */}
            <motion.div 
              className="md:w-1/3 mb-8 md:mb-0"
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
            >
              <h2 className="text-2xl font-bold text-gray-900 mb-6">{t('contact.methods.title', localeTyped)}</h2>
              <div className="space-y-4">
                <ContactMethod
                  icon="✉️"
                  title={t('contact.methods.email', localeTyped)}
                  value="<EMAIL>"
                  link="mailto:<EMAIL>"
                />
                <ContactMethod
                  icon="🕙"
                  title={t('contact.methods.workTime', localeTyped)}
                  value={t('contact.methods.workHours', localeTyped)}
                />
              </div>

            </motion.div>

            {/* 联系表单 */}
            <motion.div 
              className="md:w-2/3"
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-6 rounded-lg mb-6">
                <h2 className="text-2xl font-bold text-white mb-2">{t('contact.form.title', localeTyped)}</h2>
                <p className="text-blue-100">{t('contact.form.subtitle', localeTyped)}</p>
              </div>
              
              {submitted ? (
                <motion.div 
                  className="bg-gradient-to-r from-green-400 to-green-500 text-white p-6 rounded-lg shadow-lg"
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5 }}
                >
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <svg className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h3 className="text-lg font-bold">{t('contact.form.success.title', localeTyped)}</h3>
                      <p className="mt-1 text-green-100">
                        {t('contact.form.success.message', localeTyped)}
                      </p>
                      <p className="mt-1 text-sm text-green-200">
                        {t('contact.form.success.urgent', localeTyped)}
                      </p>
                    </div>
                  </div>
                </motion.div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                      {t('contact.form.name', localeTyped)} <span className="text-red-500">{t('contact.form.required', localeTyped)}</span>
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      className={`w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary ${
                        formErrors.name ? "border-red-500" : "border-gray-300"
                      }`}
                    />
                    {formErrors.name && (
                      <p className="mt-1 text-sm text-red-500">{formErrors.name}</p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                      {t('contact.form.email', localeTyped)} <span className="text-red-500">{t('contact.form.required', localeTyped)}</span>
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      className={`w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary ${
                        formErrors.email ? "border-red-500" : "border-gray-300"
                      }`}
                    />
                    {formErrors.email && (
                      <p className="mt-1 text-sm text-red-500">{formErrors.email}</p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                      {t('contact.form.phone', localeTyped)}
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                    />
                  </div>

                  <div>
                    <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
                      {t('contact.form.message', localeTyped)} <span className="text-red-500">{t('contact.form.required', localeTyped)}</span>
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      rows={5}
                      value={formData.message}
                      onChange={handleInputChange}
                      className={`w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary ${
                        formErrors.message ? "border-red-500" : "border-gray-300"
                      }`}
                    ></textarea>
                    {formErrors.message && (
                      <p className="mt-1 text-sm text-red-500">{formErrors.message}</p>
                    )}
                  </div>

                  <div>
                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className="w-full md:w-auto px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold text-lg rounded-lg hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-4 focus:ring-blue-300 focus:ring-offset-2 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                    >
                      {isSubmitting ? (
                        <span className="flex items-center">
                          <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          {localeTyped === 'zh' ? '提交中...' : 'Submitting...'}
                        </span>
                      ) : (
                        t('contact.form.send', localeTyped)
                      )}
                    </button>
                  </div>
                </form>
              )}
            </motion.div>
          </div>
        </div>
      </section>

      {/* 常见问题 */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div 
            className="text-center mb-12"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
          >
            <h2 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent drop-shadow-sm mb-4">{t('contact.faq.title', localeTyped)}</h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              {t('contact.faq.subtitle', localeTyped)}
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 gap-8">
            {faqs.map((item, index) => (
              <motion.div 
                key={index}
                className="bg-white p-6 rounded-lg shadow-md"
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
                variants={fadeInUp}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <h3 className="text-lg font-medium text-gray-900 mb-3">{item.question}</h3>
                <p className="text-gray-600">{item.answer}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
    </>
  );
} 