'use client';

import { motion } from "framer-motion";
import { useParams } from "next/navigation";
import { t, type Locale } from '@/lib/i18n';

// 动画配置
const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

// 核心价值观组件
const ValueCard = ({ title, description, icon }: { title: string; description: string; icon: string }) => (
  <motion.div 
    className="flex items-start p-6 bg-white rounded-lg shadow-md"
    initial="hidden"
    whileInView="visible"
    viewport={{ once: true }}
    variants={fadeInUp}
    transition={{ duration: 0.5 }}
    whileHover={{ y: -5 }}
  >
    <div className="text-3xl text-primary mr-4">{icon}</div>
    <div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">{title}</h3>
      <p className="text-gray-600">{description}</p>
    </div>
  </motion.div>
);

// 团队成员组件
const TeamMember = ({ name, role, description }: { name: string; role: string; description: string }) => (
  <motion.div 
    className="flex flex-col items-center text-center p-6"
    initial="hidden"
    whileInView="visible"
    viewport={{ once: true }}
    variants={fadeInUp}
    transition={{ duration: 0.5 }}
  >
    <div className="w-32 h-32 bg-gray-200 rounded-full mb-4 flex items-center justify-center text-4xl font-bold text-primary">
      {name.charAt(0)}
    </div>
    <h3 className="text-xl font-medium text-gray-900 mb-1">{name}</h3>
    <p className="text-gray-500 mb-3">{role}</p>
    <p className="text-gray-600">{description}</p>
  </motion.div>
);

export default function About() {
  const params = useParams();
  const locale = params.locale as string;
  const localeTyped = locale as Locale;

  // 核心价值观数据
  const values = [
    {
      title: t('about.values.items.professional.title', localeTyped),
      description: t('about.values.items.professional.description', localeTyped),
      icon: "🏆"
    },
    {
      title: t('about.values.items.efficient.title', localeTyped),
      description: t('about.values.items.efficient.description', localeTyped),
      icon: "⚡"
    },
    {
      title: t('about.values.items.customerFirst.title', localeTyped),
      description: t('about.values.items.customerFirst.description', localeTyped),
      icon: "🤝"
    }
  ];

  // 团队成员数据
  const teamMembers = [
    {
      name: t('about.team.members.founder.name', localeTyped),
      role: t('about.team.members.founder.role', localeTyped),
      description: t('about.team.members.founder.description', localeTyped)
    },
    {
      name: t('about.team.members.designer.name', localeTyped),
      role: t('about.team.members.designer.role', localeTyped),
      description: t('about.team.members.designer.description', localeTyped)
    },
    {
      name: t('about.team.members.backend.name', localeTyped),
      role: t('about.team.members.backend.role', localeTyped),
      description: t('about.team.members.backend.description', localeTyped)
    }
  ];

  return (
    <>
      {/* 页面标题 */}
      <section className="bg-gradient-to-r from-primary to-blue-700 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.h1 
            className="text-3xl md:text-4xl font-bold mb-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            {t('about.title', localeTyped)}
          </motion.h1>
          <motion.p 
            className="text-xl text-blue-100 max-w-3xl"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            {t('about.subtitle', localeTyped)}
          </motion.p>
        </div>
      </section>

      {/* 工作室简介 */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="md:flex md:items-center md:space-x-12">
            <motion.div 
              className="md:w-1/2 mb-8 md:mb-0"
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
            >
              <h2 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent drop-shadow-sm mb-6">
                {t('about.introduction.title', localeTyped)}
              </h2>
              <div className="text-gray-600 space-y-4">
                <p>
                  {t('about.introduction.paragraphs.first', localeTyped)}
                </p>
                <p>
                  {t('about.introduction.paragraphs.second', localeTyped)}
                </p>
                <p>
                  {t('about.introduction.paragraphs.third', localeTyped)}
                </p>
              </div>
            </motion.div>
            <motion.div 
              className="md:w-1/2"
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              {/* 可以放置一张团队照片或办公环境图片 */}
              <div className="bg-gray-200 rounded-lg h-80 flex items-center justify-center text-7xl">
                🏢
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* 核心价值观 */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div 
            className="text-center mb-12"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
          >
            <h2 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent drop-shadow-sm mb-4">
              {t('about.values.title', localeTyped)}
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              {t('about.values.subtitle', localeTyped)}
            </p>
          </motion.div>
          <div className="grid md:grid-cols-3 gap-8">
            {values.map((value, index) => (
              <ValueCard 
                key={`${index}-${locale}`}
                title={value.title} 
                description={value.description} 
                icon={value.icon}
              />
            ))}
          </div>
        </div>
      </section>

      {/* 团队介绍 */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div 
            className="text-center mb-12"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
          >
            <h2 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent drop-shadow-sm mb-4">
              {t('about.team.title', localeTyped)}
            </h2>
            <p className="text-3xl font-bold mb-8">
              {t('about.team.subtitle', localeTyped)}
            </p>
          </motion.div>
          <div className="grid md:grid-cols-3 gap-8">
            {teamMembers.map((member, index) => (
              <TeamMember 
                key={`${index}-${locale}`}
                name={member.name} 
                role={member.role} 
                description={member.description} 
              />
            ))}
          </div>
        </div>
      </section>
    </>
  );
} 