'use client';

import Link from "next/link";
import { motion } from "framer-motion";
import { useParams } from "next/navigation";
import { t, type Locale } from '@/lib/i18n';
import {
  AppDevIcon,
  MiniProgramIcon,
  BackendIcon,
  WebDevIcon,
  GlobalPlatformsIcon,
  StarIcon,
  QuoteIcon,
  NavigationIcons
} from '@/components/ui/Icons';
import Card, { CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';

// 动画配置
const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

// 服务卡片组件
const ServiceCard = ({
  title,
  description,
  IconComponent
}: {
  title: string;
  description: string;
  IconComponent: React.ComponentType<any>;
}) => (
  <Card hover className="group">
    <CardContent className="text-center">
      <div className="mb-6 flex justify-center">
        <div className="p-4 bg-gradient-to-br from-primary-50 to-primary-100 rounded-2xl group-hover:from-primary-100 group-hover:to-primary-200 transition-all duration-300">
          <IconComponent className="w-8 h-8 text-primary-600" />
        </div>
      </div>
      <CardTitle size="md" className="mb-3 group-hover:text-primary-600 transition-colors">
        {title}
      </CardTitle>
      <CardDescription className="text-sm leading-relaxed">
        {description}
      </CardDescription>
    </CardContent>
  </Card>
);

// 评价卡片组件
const TestimonialCard = ({ name, role, content }: { name: string; role: string; content: string }) => (
  <Card hover className="relative overflow-hidden">
    <CardContent>
      <div className="absolute top-4 right-4 opacity-20">
        <QuoteIcon className="w-8 h-8 text-primary-600" />
      </div>
      <CardDescription className="text-base italic mb-6 leading-relaxed">
        &ldquo;{content}&rdquo;
      </CardDescription>
      <div className="flex items-center">
        <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center text-white font-semibold text-lg shadow-lg">
          {name.charAt(0)}
        </div>
        <div className="ml-4">
          <p className="font-semibold text-gray-900">{name}</p>
          <p className="text-primary-600 text-sm font-medium">{role}</p>
        </div>
      </div>
    </CardContent>
  </Card>
);

export default function Home() {
  const params = useParams();
  const locale = params.locale as string;
  const localeTyped = locale as Locale;

  // 服务数据
  const services = [
    {
      title: t('home.services.appDev.title', localeTyped),
      description: t('home.services.appDev.description', localeTyped),
      IconComponent: AppDevIcon
    },
    {
      title: t('home.services.miniProgram.title', localeTyped),
      description: t('home.services.miniProgram.description', localeTyped),
      IconComponent: MiniProgramIcon
    },
    {
      title: t('home.services.backend.title', localeTyped),
      description: t('home.services.backend.description', localeTyped),
      IconComponent: BackendIcon
    },
    {
      title: t('home.services.webDev.title', localeTyped),
      description: t('home.services.webDev.description', localeTyped),
      IconComponent: WebDevIcon
    },
    {
      title: t('home.services.globalPlatforms.title', localeTyped),
      description: t('home.services.globalPlatforms.description', localeTyped),
      IconComponent: GlobalPlatformsIcon
    }
  ];

  // 客户评价
  const testimonials = localeTyped === 'zh' ? [
    {
      name: "王先生",
      role: "科技公司 CEO",
      content: "三娃软件团队专业高效，交付的小程序超出了我们的预期。客户反馈非常积极！"
    },
    {
      name: "李女士",
      role: "电商平台负责人", 
      content: "与三娃软件的合作非常愉快，他们提供的后端解决方案稳定可靠，支持了我们业务的快速发展。"
    }
  ] : [
    {
      name: "John Smith",
      role: "Tech Company CEO",
      content: "Sanva's team is professional and efficient. The mini-program they delivered exceeded our expectations. Customer feedback is very positive!"
    },
    {
      name: "Sarah Johnson",
      role: "E-commerce Platform Manager",
      content: "Working with Sanva has been a pleasure. Their backend solutions are stable and reliable, supporting our rapid business growth."
    }
  ];

  return (
    <>
      {/* Hero 区域 */}
      <section className="bg-gradient-to-br from-primary to-blue-700 text-white py-16 md:py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="md:flex md:items-center md:justify-between">
            <div className="md:w-1/2">
              <motion.h1 
                className="text-3xl md:text-5xl font-bold mb-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                {t('home.hero.title', localeTyped)}
              </motion.h1>
              <motion.p 
                className="text-xl mb-8 text-blue-100"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                {t('home.hero.subtitle', localeTyped)}
              </motion.p>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
              >
                <Link href={`/${locale}/contact`} className="bg-secondary hover:bg-green-600 text-white font-bold py-3 px-6 rounded-md transition-colors duration-300">
                  {t('home.hero.cta', localeTyped)}
                </Link>
              </motion.div>
            </div>
            <div className="hidden md:block md:w-1/2">
              <div className="h-64 flex items-center justify-center">
                <span className="text-9xl">💻✨</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 服务介绍区域 */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div 
            className="text-center mb-12"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
          >
            <h2 className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent drop-shadow-sm mb-4">{t('home.services.title', localeTyped)}</h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              {t('home.services.subtitle', localeTyped)}
            </p>
          </motion.div>
          <div className="grid md:grid-cols-3 lg:grid-cols-5 gap-6">
            {services.map((service, index) => (
              <ServiceCard 
                key={`${index}-${locale}`} 
                title={service.title} 
                description={service.description} 
                icon={service.icon} 
              />
            ))}
          </div>
        </div>
      </section>

      {/* 客户评价区域 */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div 
            className="text-center mb-12"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
          >
            <h2 className="text-3xl font-bold mb-8">{t('home.testimonials.title', localeTyped)}</h2>
            <p className="text-3xl font-bold mb-8">
              {t('home.testimonials.subtitle', localeTyped)}
            </p>
          </motion.div>
          <div className="grid md:grid-cols-2 gap-8">
            {testimonials.map((testimonial, index) => (
              <TestimonialCard 
                key={`${index}-${locale}`} 
                name={testimonial.name} 
                role={testimonial.role} 
                content={testimonial.content} 
              />
            ))}
          </div>
        </div>
      </section>

      {/* 快速导航区域 */}
      <section className="py-16 bg-primary text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.h2 
            className="text-3xl font-bold mb-8"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
          >
            {t('home.quickNav.title', localeTyped)}
          </motion.h2>
          <motion.div 
            className="grid sm:grid-cols-3 gap-4"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
            transition={{ duration: 0.5, delay: 0.2 }}
            key={`quicknav-${locale}`}
          >
            <Link href={`/${locale}/about`} className="bg-white/10 hover:bg-white/20 backdrop-blur text-white py-8 px-6 rounded-lg transition-colors duration-300">
              <div className="text-3xl mb-4">🏢</div>
              <h3 className="text-lg font-medium mb-2">
                {localeTyped === 'zh' ? '关于我们' : 'About Us'}
              </h3>
              <p className="text-blue-100 text-sm">
                {localeTyped === 'zh' ? '了解我们的团队和使命' : 'Learn about our team and mission'}
              </p>
            </Link>
            <Link href={`/${locale}/services`} className="bg-white/10 hover:bg-white/20 backdrop-blur text-white py-8 px-6 rounded-lg transition-colors duration-300">
              <div className="text-3xl mb-4">⚙️</div>
              <h3 className="text-lg font-medium mb-2">
                {localeTyped === 'zh' ? '服务详情' : 'Our Services'}
              </h3>
              <p className="text-blue-100 text-sm">
                {localeTyped === 'zh' ? '查看我们的详细服务内容' : 'View our detailed service offerings'}
              </p>
            </Link>
            <Link href={`/${locale}/contact`} className="bg-white/10 hover:bg-white/20 backdrop-blur text-white py-8 px-6 rounded-lg transition-colors duration-300">
              <div className="text-3xl mb-4">📞</div>
              <h3 className="text-lg font-medium mb-2">
                {localeTyped === 'zh' ? '联系我们' : 'Contact Us'}
              </h3>
              <p className="text-blue-100 text-sm">
                {localeTyped === 'zh' ? '开始您的项目咨询' : 'Start your project consultation'}
              </p>
            </Link>
          </motion.div>
        </div>
      </section>
    </>
  );
}
