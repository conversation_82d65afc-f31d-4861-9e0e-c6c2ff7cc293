'use client';

import { motion } from "framer-motion";
import Link from "next/link";
import { useParams } from "next/navigation";
import { t, type Locale } from '@/lib/i18n';

// 动画配置
const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

const staggerChildren = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

// 服务特性组件
const ServiceFeature = ({ icon, title, description }: { icon: string; title: string; description: string }) => (
  <motion.div
    className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow"
    variants={fadeInUp}
    whileHover={{ y: -5 }}
  >
    <div className="text-3xl text-primary mb-4">{icon}</div>
    <h3 className="text-lg font-medium text-gray-900 mb-2">{title}</h3>
    <p className="text-gray-600">{description}</p>
  </motion.div>
);

// 流程步骤组件
const ProcessStep = ({ step, title, description, isLast }: { step: number; title: string; description: string; isLast?: boolean }) => (
  <motion.div
    className="flex items-start"
    variants={fadeInUp}
  >
    <div className="flex flex-col items-center mr-4">
      <div className="w-10 h-10 bg-primary text-white rounded-full flex items-center justify-center font-bold">
        {step}
      </div>
      {!isLast && <div className="w-0.5 h-16 bg-gray-300 mt-4"></div>}
    </div>
    <div className="flex-1">
      <h3 className="text-lg font-medium text-gray-900 mb-2">{title}</h3>
      <p className="text-gray-600">{description}</p>
    </div>
  </motion.div>
);

// 服务卡片组件
const ServiceCard = ({ title, description, features, icon }: { 
  title: string; 
  description: string; 
  features: string[]; 
  icon: string 
}) => (
  <motion.div
    className="bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-shadow"
    variants={fadeInUp}
    whileHover={{ y: -5 }}
  >
    <div className="text-5xl text-primary mb-6 text-center">{icon}</div>
    <h3 className="text-2xl font-bold text-gray-900 mb-4 text-center">{title}</h3>
    <p className="text-gray-600 mb-6 text-center">{description}</p>
    <ul className="space-y-3">
      {features.map((feature, index) => (
        <li key={index} className="flex items-center text-gray-700">
          <svg className="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
          {feature}
        </li>
      ))}
    </ul>
  </motion.div>
);

export default function Services() {
  const params = useParams();
  const locale = params.locale as string;
  const localeTyped = locale as Locale;

  // 主要服务数据
  const mainServices = [
    {
      title: t('services.appDevelopment.title', localeTyped),
      description: t('services.appDevelopment.description', localeTyped),
      icon: "📱",
      features: [
        t('services.appDevelopment.features.native', localeTyped),
        t('services.appDevelopment.features.crossPlatform', localeTyped),
        t('services.appDevelopment.features.uiUx', localeTyped),
        t('services.appDevelopment.features.maintenance', localeTyped),
        t('services.appDevelopment.features.storeSupport', localeTyped)
      ]
    },
    {
      title: t('services.miniProgram.title', localeTyped),
      description: t('services.miniProgram.description', localeTyped),
      icon: "💻",
      features: [
        t('services.miniProgram.features.wechat', localeTyped),
        t('services.miniProgram.features.alipay', localeTyped),
        t('services.miniProgram.features.douyin', localeTyped),
        t('services.miniProgram.features.multiPlatform', localeTyped),
        t('services.miniProgram.features.ecommerce', localeTyped)
      ]
    },
    {
      title: t('services.backend.title', localeTyped),
      description: t('services.backend.description', localeTyped),
      icon: "🖥️",
      features: [
        t('services.backend.features.api', localeTyped),
        t('services.backend.features.database', localeTyped),
        t('services.backend.features.auth', localeTyped),
        t('services.backend.features.cloud', localeTyped),
        t('services.backend.features.server', localeTyped)
      ]
    },
    {
      title: t('services.globalPlatforms.title', localeTyped),
      description: t('services.globalPlatforms.description', localeTyped),
      icon: "🌍",
      features: [
        t('services.globalPlatforms.features.googlePlay', localeTyped),
        t('services.globalPlatforms.features.appStore', localeTyped),
        t('services.globalPlatforms.features.facebook', localeTyped),
        t('services.globalPlatforms.features.whatsapp', localeTyped),
        t('services.globalPlatforms.features.telegram', localeTyped),
        t('services.globalPlatforms.features.instagram', localeTyped),
        t('services.globalPlatforms.features.twitter', localeTyped),
        t('services.globalPlatforms.features.linkedin', localeTyped)
      ]
    }
  ];

  // 流程步骤数据
  const processSteps = [
    {
      title: t('services.process.steps.analysis.title', localeTyped),
      description: t('services.process.steps.analysis.description', localeTyped)
    },
    {
      title: t('services.process.steps.design.title', localeTyped),
      description: t('services.process.steps.design.description', localeTyped)
    },
    {
      title: t('services.process.steps.development.title', localeTyped),
      description: t('services.process.steps.development.description', localeTyped)
    },
    {
      title: t('services.process.steps.delivery.title', localeTyped),
      description: t('services.process.steps.delivery.description', localeTyped)
    }
  ];

  return (
    <>
      {/* 页面标题 */}
      <section className="bg-gradient-to-r from-primary to-blue-700 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.h1 
            className="text-3xl md:text-4xl font-bold mb-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            {t('services.title', localeTyped)}
          </motion.h1>
          <motion.p 
            className="text-xl text-blue-100 max-w-3xl"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            {t('services.subtitle', localeTyped)}
          </motion.p>
        </div>
      </section>

      {/* 服务简介 */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div 
            className="text-center mb-12"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
          >
            <h2 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent drop-shadow-sm mb-4">
              {t('services.sectionTitle', localeTyped)}
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              {t('services.sectionSubtitle', localeTyped)}
            </p>
          </motion.div>
        </div>
      </section>

      {/* 服务详情 */}
      <section className="pb-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 gap-8">
            {mainServices.map((service, index) => (
              <ServiceCard
                key={index}
                title={service.title}
                description={service.description}
                icon={service.icon}
                features={service.features}
              />
            ))}
          </div>
        </div>
      </section>

      {/* 开发流程 */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div 
            className="text-center mb-12"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
          >
            <h2 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent drop-shadow-sm mb-4">
              {t('services.process.title', localeTyped)}
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              {t('services.process.subtitle', localeTyped)}
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {processSteps.map((step, index) => (
              <ProcessStep
                key={index}
                step={index + 1}
                title={step.title}
                description={step.description}
                isLast={index === processSteps.length - 1}
              />
            ))}
          </div>
        </div>
      </section>

      {/* CTA区域 */}
      <section className="py-16 bg-primary text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
          >
            <h2 className="text-2xl md:text-3xl font-bold mb-4">
              {localeTyped === 'zh' ? '准备开始您的项目？' : 'Ready to Start Your Project?'}
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              {localeTyped === 'zh' 
                ? '联系我们，让我们为您打造专业的数字解决方案' 
                : 'Contact us and let us create professional digital solutions for you'}
            </p>
            <Link
              href={`/${locale}/contact`}
              className="bg-secondary hover:bg-green-600 text-white font-bold py-3 px-8 rounded-md transition-colors duration-300 inline-block"
            >
              {localeTyped === 'zh' ? '立即咨询' : 'Contact Now'}
            </Link>
          </motion.div>
        </div>
      </section>
    </>
  );
} 