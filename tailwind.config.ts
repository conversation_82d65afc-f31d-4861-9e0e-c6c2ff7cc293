import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: "#1E3A8A", // 深蓝
        secondary: "#10B981", // 强调色
        gray: "#4B5563", // 灰色
      },
      fontFamily: {
        sans: ["var(--font-notosans)", "var(--font-roboto)", "sans-serif"],
      },
    },
  },
  plugins: [],
};

export default config; 