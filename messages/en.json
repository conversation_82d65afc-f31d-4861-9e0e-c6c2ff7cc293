{"common": {"contactUs": "Contact Us", "services": "Services", "about": "About", "home": "Home", "learnMore": "Learn More", "loading": "Loading...", "submit": "Submit", "submitting": "Submitting..."}, "navigation": {"home": "Home", "services": "Services", "about": "About Us", "contact": "Contact"}, "home": {"hero": {"title": "Crafting Efficient & Elegant Digital Solutions", "subtitle": "Sanva Software Development Studio specializes in providing professional app development, mini-program development, and backend development services for businesses and individuals.", "cta": "Contact Us"}, "services": {"title": "Our Services", "subtitle": "Comprehensive software development solutions to meet all your digital needs", "appDev": {"title": "App Development", "description": "Native iOS/Android or cross-platform app development using React Native, Flutter, and other tech stacks"}, "miniProgram": {"title": "Mini-Program Development", "description": "WeChat Mini-Programs, Alipay Mini-Programs, TikTok Mini-Programs, and more with fast deployment and excellent user experience"}, "backend": {"title": "Backend Development", "description": "RESTful APIs, database design, cloud services (AWS, Alibaba Cloud, etc.) solutions"}, "webDev": {"title": "Web Development", "description": "Responsive websites, corporate websites, e-commerce platforms supporting multi-device access"}, "globalPlatforms": {"title": "Global Platform Development", "description": "Google Play apps, App Store apps, Facebook mini-games, WhatsApp Business, and other global platform development"}}, "testimonials": {"title": "Client Testimonials", "subtitle": "What our clients say about our services"}, "quickNav": {"title": "Learn More About Us"}}, "services": {"title": "Our Services", "subtitle": "We provide professional software development services including app development, mini-program development, and backend development", "sectionTitle": "Comprehensive Software Development Solutions", "sectionSubtitle": "Whether you need mobile apps, mini-programs, or backend systems, we provide quality development services to help your business achieve digital transformation.", "appDevelopment": {"title": "App Development", "description": "We provide native iOS and Android app development services, as well as cross-platform app development using modern technologies.", "features": {"native": "Native App Development (iOS/Android)", "crossPlatform": "Cross-Platform App Development", "uiUx": "UI/UX Design", "maintenance": "App Maintenance & Updates", "storeSupport": "App Store Submission Support"}}, "miniProgram": {"title": "Mini-Program Development", "description": "We focus on developing various mini-programs including WeChat, Alipay, and TikTok mini-programs to help businesses reach users quickly.", "features": {"wechat": "WeChat Mini-Program Development", "alipay": "Alipay Mini-Program Development", "douyin": "TikTok Mini-Program Development", "multiPlatform": "Multi-Platform Mini-Program Solutions", "ecommerce": "Mini-Program E-commerce & Payment Integration"}}, "backend": {"title": "Backend Development", "description": "We provide reliable, secure, and high-performance backend development services to power your applications.", "features": {"api": "RESTful API Development", "database": "Database Design & Optimization", "auth": "User Authentication & Authorization", "cloud": "Cloud Service Integration (AWS, Alibaba Cloud, etc.)", "server": "Server Configuration & Maintenance"}}, "globalPlatforms": {"title": "Global Platform Development", "description": "We provide development services for global platforms to help your products reach users worldwide.", "features": {"googlePlay": "Google Play App Development", "appStore": "App Store App Development", "facebook": "Facebook Mini-Game Development", "whatsapp": "WhatsApp Business Integration", "telegram": "Telegram Bot Development", "instagram": "Instagram API Integration", "twitter": "Twitter/X API Integration", "linkedin": "LinkedIn App Development"}}, "process": {"title": "Our Development Process", "subtitle": "Transparent and efficient development process ensuring smooth project progress and timely delivery", "steps": {"analysis": {"title": "Requirements Analysis", "description": "Deep understanding of your business needs, defining project goals and feature scope"}, "design": {"title": "Design & Planning", "description": "Creating technical solutions and design prototypes to ensure optimal user experience"}, "development": {"title": "Development Implementation", "description": "Development according to design plans with regular progress reports and deliverables"}, "delivery": {"title": "Testing & Delivery", "description": "Comprehensive testing of application features, ensuring quality before deployment and delivery"}}}}, "contact": {"title": "Contact Us", "subtitle": "We look forward to hearing your needs and providing professional support anytime", "methods": {"title": "Contact Information", "email": "Email", "workTime": "Business Hours", "workHours": "Monday to Friday 9:00 - 18:00"}, "followUs": "Follow Us", "form": {"title": "Send Message", "subtitle": "We look forward to your message. Our professional team will provide customized solutions for you", "name": "Name", "email": "Email", "phone": "Phone (Optional)", "message": "Requirements Description", "required": "*", "send": "💌 Send Now", "success": {"title": "🎉 Thank you for your message!", "message": "We have received your message. Our professional team will contact you within 24 hours.", "urgent": "For urgent needs, please send email <NAME_EMAIL>"}, "errors": {"nameRequired": "Please enter your name", "emailRequired": "Please enter your email", "emailInvalid": "Please enter a valid email address", "messageRequired": "Please enter your requirements description"}}, "faq": {"title": "Frequently Asked Questions", "subtitle": "Here are some frequently asked questions from our clients. If you have other questions, please feel free to contact us", "questions": {"timeline": {"q": "How long does project development typically take?", "a": "Project development timeline depends on project scale and complexity. Simple mini-programs may take 2-4 weeks, while complex applications may require 2-3 months. We provide detailed time estimates before project start."}, "pricing": {"q": "What are your pricing standards?", "a": "We determine pricing based on project complexity, feature count, and development time. We provide free project evaluation and quotation services. Please contact us for detailed information."}, "maintenance": {"q": "Do you provide maintenance services after development?", "a": "Yes, we provide technical support and maintenance services after project delivery. We usually provide 1-3 months of free maintenance period, followed by long-term maintenance contracts."}, "modification": {"q": "Can you modify existing apps or mini-programs?", "a": "Yes. We can take over and modify existing projects, optimize features, or fix issues. We first evaluate the code, then provide modification plans and quotations."}}}}, "about": {"title": "About Us", "subtitle": "Learn about Sanva Software Development Studio's background and professional capabilities", "introduction": {"title": "Studio Introduction", "paragraphs": {"first": "Sanva Software Development Studio was founded in 2023, specializing in providing high-quality software solutions for businesses and individuals. Our services cover mobile app development, mini-program development, and backend system development.", "second": "Our team consists of experienced development engineers and designers, each with solid technical skills and innovative thinking. We focus on cutting-edge technology development, continuously learning and applying new technologies to ensure we provide the highest quality services to our clients.", "third": "At Sanva Software, we believe technology should serve people and create value for businesses. We are committed to solving real problems with technology, helping clients achieve digital transformation, improve operational efficiency, and enhance market competitiveness."}}, "values": {"title": "Our Core Values", "subtitle": "These values guide our daily work and help us provide the highest quality services to our clients", "items": {"professional": {"title": "Professional", "description": "We have extensive technical experience and industry knowledge to provide professional software solutions for our clients."}, "efficient": {"title": "Efficient", "description": "We focus on development efficiency and project progress management to ensure timely delivery of high-quality products."}, "customerFirst": {"title": "Customer First", "description": "We center on customer needs, providing personalized solutions and attentive after-sales service."}}}, "team": {"title": "Our Team", "subtitle": "Composed of experienced professionals dedicated to providing you with the highest quality services", "members": {"founder": {"name": "<PERSON>", "role": "Founder / Lead Dev<PERSON><PERSON>", "description": "With 10 years of software development experience, specializing in mobile app and mini-program development."}, "designer": {"name": "<PERSON>", "role": "UI/UX Designer", "description": "Skilled in creating beautiful and user-friendly interfaces, focusing on every detail of user experience."}, "backend": {"name": "<PERSON>", "role": "Backend Developer", "description": "Expert in cloud services and database design, building stable and efficient backend systems."}}}}}